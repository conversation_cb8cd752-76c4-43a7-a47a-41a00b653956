# Gym Management System - Frontend Implementation Guide

## Important Notes

- Note before u begin i aready have client folder which has old code, feel free to update if the structure does not match with this guid
- If u need any clarification ask me ones i anwer continue dont stop to wast credit, i only have max of 20 to use , so please work smart and dont wast if for me

# API BASE URL IS http://0.0.0.0:8000

- All server code and API implementations are available in the `server/` folder for reference
- Minimal comments - use clear, self-documenting code with proper naming conventions
- **Member UI is mobile-first**: Optimize for phone, then tablet, then desktop
- **Staff UI is desktop-first**: Standard admin dashboard layout
- All list endpoints use FastAPI pagination: `from fastapi_pagination import Page, paginate`
- Handle paginated responses with `items`, `total`, `page`, `size`, `pages` fields

## System Overview

**Authentication Model:**

- **Users (Staff)**: Trainers, Receptionists, Managers, Admins - have login credentials and role-based access
- **Members (Customers)**: **NO LOGIN REQUIRED** - identified by phone/email for bookings and payments

**Core User Flows:**

1. **Staff Portal**: Login → Dashboard → Manage operations (requires authentication)
2. **Member Portal**: No login → Search self by phone/email → Book class/session → Make payment (public access)

---

## API Endpoints Reference

### Authentication

```
POST /auth/register/          - Register new staff user
POST /auth/gimme-jwt/         - Staff login (returns JWT token)
```

### Users (Staff Management)

```
GET  /accounts/               - List all staff users (filterable)
GET  /accounts/me/            - Get current logged-in user
PUT  /accounts/{user_id}/     - Update staff user
DELETE /accounts/{user_id}/   - Delete staff user
```

### Members (Public - No Auth Required)

```
POST /members/                - Create new member (or return existing if email/phone exists)
GET  /members/                - List members (search by name, filter by active, phone, email)
GET  /members/{member_id}/    - Get member details
PUT  /members/{member_id}/    - Update member (public access)
DELETE /members/{member_id}/  - Delete member (staff only)
```

### Membership Plans (Public - No Auth Required)

```
POST /membership-plans/       - Create plan (staff only)
GET  /membership-plans/       - List all plans (public - for members to view)
GET  /membership-plans/{id}/  - Get plan details (public)
PUT  /membership-plans/{id}/  - Update plan (staff only)
DELETE /membership-plans/{id}/- Delete plan (staff only)
```

### Attendance (Public - No Auth Required)

```
POST /attendance/check-in/    - Member check-in (public)
PATCH /attendance/{id}/check-out/ - Member check-out (public)
GET  /attendance/             - List attendance (filter by member, date, status)
GET  /attendance/{id}/        - Get attendance record
DELETE /attendance/{id}/      - Delete attendance record (staff only)
```

### Payments (Public - No Auth Required)

```
POST /payments/               - Create payment (member_id, payment_method, optional attendance_id) (public)
GET  /payments/               - List payments (filter by member, status, date range)
GET  /payments/{id}/          - Get payment details
DELETE /payments/{id}/        - Delete payment (staff only)
```

### Classes (Public - No Auth Required)

```
POST /classes/                - Create gym class (staff only)
GET  /classes/                - List classes (filter by active, trainer, day) (public)
GET  /classes/{id}/           - Get class details (public)
PUT  /classes/{id}/           - Update class (staff only)
DELETE /classes/{id}/         - Delete class (staff only)
```

### Class Bookings (Public - No Auth Required)

```
POST /classes/bookings/       - Book class (gym_class_id, member_id, class_date) (public)
GET  /classes/bookings        - List bookings (filter by member, class, date, status)
GET  /classes/bookings/{id}/  - Get booking details
PATCH /classes/bookings/{id}/ - Update booking (public)
PATCH /classes/bookings/{id}/cancel/ - Cancel booking (public)
DELETE /classes/bookings/{id}/- Delete booking (staff only)
```

### Inventory

```
POST /inventory/categories/   - Create equipment category
GET  /inventory/categories/   - List categories
PUT  /inventory/categories/{id}/ - Update category
DELETE /inventory/categories/{id}/ - Delete category

POST /inventory/equipment/    - Create equipment
GET  /inventory/equipment/    - List equipment (filter by status, category, availability)
GET  /inventory/equipment/{id}/ - Get equipment details
PUT  /inventory/equipment/{id}/ - Update equipment
DELETE /inventory/equipment/{id}/ - Delete equipment

POST /inventory/equipment/{id}/maintenance/ - Log maintenance
GET  /inventory/equipment/{id}/maintenance/ - Get maintenance history
```

---

## Required React Application Structure

### 1. Technology Stack

```json
{
  "core": ["React 18+", "TypeScript"],
  "routing": "React Router v6",
  "state_management": "React Query (TanStack Query) or Redux Toolkit",
  "ui_framework": "Material-UI, Ant Design, or Shadcn/ui",
  "forms": "React Hook Form + Zod validation",
  "http_client": "Axios",
  "date_handling": "date-fns or dayjs",
  "charts": "Recharts (for dashboard analytics)"
}
```

### 2. Project Structure

```
src/
├── api/
│   ├── axios.config.ts          # Axios instance with interceptors
│   ├── auth.api.ts              # Auth endpoints
│   ├── members.api.ts           # Member endpoints
│   ├── attendance.api.ts        # Attendance endpoints
│   ├── payments.api.ts          # Payment endpoints
│   ├── classes.api.ts           # Class & booking endpoints
│   ├── inventory.api.ts         # Inventory endpoints
│   └── users.api.ts             # User management endpoints
│
├── components/
│   ├── common/
│   │   ├── Layout.tsx           # Main layout with sidebar
│   │   ├── Navbar.tsx
│   │   ├── Sidebar.tsx
│   │   ├── LoadingSpinner.tsx
│   │   ├── ErrorBoundary.tsx
│   │   └── ProtectedRoute.tsx   # Role-based route protection
│   │
│   ├── members/
│   │   ├── MemberList.tsx       # Table with search/filter
│   │   ├── MemberForm.tsx       # Create/Edit form
│   │   ├── MemberDetails.tsx    # View member info
│   │   └── MemberSelector.tsx   # Quick search dropdown
│   │
│   ├── attendance/
│   │   ├── CheckInForm.tsx      # Member check-in
│   │   ├── AttendanceList.tsx   # Active sessions
│   │   └── CheckOutButton.tsx
│   │
│   ├── payments/
│   │   ├── PaymentForm.tsx      # Process payment
│   │   ├── PaymentHistory.tsx   # Transaction list
│   │   └── PaymentReceipt.tsx
│   │
│   ├── classes/
│   │   ├── ClassList.tsx        # Schedule view
│   │   ├── ClassForm.tsx        # Create/Edit class
│   │   ├── BookingForm.tsx      # Member booking
│   │   └── ClassSchedule.tsx    # Calendar view
│   │
│   ├── inventory/
│   │   ├── EquipmentList.tsx
│   │   ├── EquipmentForm.tsx
│   │   ├── MaintenanceLog.tsx
│   │   └── CategoryManager.tsx
│   │
│   └── dashboard/
│       ├── DashboardStats.tsx   # KPI cards
│       ├── RecentActivity.tsx
│       └── Charts.tsx
│
├── pages/
│   ├── LoginPage.tsx            # Staff login only
│   ├── DashboardPage.tsx        # Staff dashboard
│   │
│   ├── member/                  # Member-facing pages (mobile-first, no auth)
│   │   ├── HomePage.tsx         # Member landing page
│   │   ├── FindMemberPage.tsx   # Search by phone/email
│   │   ├── MemberProfilePage.tsx # View own details after identification
│   │   ├── BookClassPage.tsx    # Book classes
│   │   ├── MyBookingsPage.tsx   # View/cancel bookings
│   │   ├── CheckInPage.tsx      # Self check-in
│   │   ├── PaymentPage.tsx      # Make payment
│   │   └── ClassSchedulePage.tsx # View class schedule
│   │
│   ├── staff/                   # Staff-facing pages (desktop-first, auth required)
│   │   ├── MembersPage.tsx      # Manage all members
│   │   ├── AttendancePage.tsx   # View attendance records
│   │   ├── PaymentsPage.tsx     # View all payments
│   │   ├── ClassesPage.tsx      # Manage classes
│   │   ├── InventoryPage.tsx    # Manage equipment
│   │   └── SettingsPage.tsx     # System settings
│   │
├── hooks/
│   ├── useAuth.ts               # Authentication logic
│   ├── useMembers.ts            # Member CRUD operations
│   ├── useAttendance.ts
│   ├── usePayments.ts
│   └── useClasses.ts
│
├── context/
│   └── AuthContext.tsx          # User session & permissions
│
├── types/
│   ├── user.types.ts
│   ├── member.types.ts
│   ├── attendance.types.ts
│   ├── payment.types.ts
│   ├── class.types.ts
│   └── inventory.types.ts
│
├── utils/
│   ├── formatters.ts            # Date, currency formatting
│   ├── validators.ts            # Form validation schemas
│   └── constants.ts             # Enums, status codes
│
└── App.tsx
```

---

## TypeScript Type Definitions

### Core Types

```typescript
// types/user.types.ts
export enum UserRole {
  ADMIN = "admin",
  STAFF = "staff",
  TRAINER = "trainer",
  RECEPTIONIST = "receptionist",
  MANAGER = "manager",
  ACCOUNTANT = "accountant",
}

export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string | null;
  role: UserRole;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// types/member.types.ts
export interface Member {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  date_of_birth: string | null;
  gender: string | null;
  address: string | null;
  emergency_contact: string | null;
  membership_plan: MembershipPlan | null;
  membership_start_date: string | null;
  membership_end_date: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MembershipPlan {
  id: string;
  name: string;
  price: number;
  duration_days: number;
  description: string | null;
}

// types/attendance.types.ts
export interface Attendance {
  id: string;
  member: Member;
  check_in_time: string;
  check_out_time: string | null;
  duration_minutes: number | null;
  notes: string | null;
  checked_out: boolean;
  created_at: string;
  updated_at: string;
}

// types/payment.types.ts
export enum PaymentStatus {
  PENDING = "pending",
  PAID = "paid",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
}

export interface Payment {
  id: string;
  member: Member;
  amount: number;
  payment_date: string;
  payment_method: string;
  transaction_id: string | null;
  status: PaymentStatus;
  notes: string | null;
  attendance_id: string | null;
  created_at: string;
  updated_at: string;
}

// types/class.types.ts
export enum BookingStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  CHECKED_IN = "checked_in",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  NO_SHOW = "no_show",
}

export interface GymClass {
  id: string;
  name: string;
  description: string | null;
  trainer: User | null;
  capacity: number;
  duration_minutes: number;
  day_of_week: string;
  start_time: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ClassBooking {
  id: string;
  gym_class: GymClass;
  member: Member;
  booking_date: string;
  class_date: string;
  status: BookingStatus;
  created_at: string;
  updated_at: string;
}

// types/inventory.types.ts
export enum EquipmentStatus {
  OPERATIONAL = "operational",
  MAINTENANCE = "maintenance",
  BROKEN = "broken",
}

export interface Equipment {
  id: string;
  name: string;
  category: EquipmentCategory | null;
  brand: string | null;
  serial_number: string | null;
  purchase_date: string | null;
  status: EquipmentStatus;
  condition: string;
  location: string | null;
  last_maintenance_date: string | null;
  next_maintenance_date: string | null;
  quantity: number;
  is_available: boolean;
}

export interface EquipmentCategory {
  id: string;
  name: string;
  description: string | null;
}
```

---

## Key Features & User Flows

### MEMBER FLOWS (Public Access - No Authentication)

#### 1. **Member Identification Flow**

```
Step 1: Search for yourself
- Enter phone or email
- GET /members/?phone={phone} OR GET /members/?email={email}
- System returns matching member

Step 2: Confirm identity
- Display member details (name, plan, expiry)
- Member confirms "Yes, this is me"
- Store member_id in session/state for subsequent actions
```

#### 2. **Member Check-In Flow**

```
Flow:
1. Member identifies themselves (phone/email search)
2. System validates:
   - Member is active
   - Membership not expired
   - No active check-in session
3. Click "Check In" → POST /attendance/check-in/
   Body: { member: member_id, notes: "" }
4. Show confirmation with check-in time
5. Option to make payment if needed
```

#### 3. **Member Self-Checkout Flow**

```
Flow:
1. Member identifies themselves
2. GET /attendance/?member_id={id}&checked_out=false (find active session)
3. Click "Check Out" → PATCH /attendance/{id}/check-out/
4. Show duration and summary
```

#### 4. **Member Class Booking Flow**

```
Flow:
1. Member identifies themselves
2. View available classes → GET /classes/?is_active=true
3. Select class and date
4. Click "Book" → POST /classes/bookings/
   Body: {
     gym_class_id: uuid,
     member_id: uuid,
     class_date: "2025-01-15"
   }
5. System validates:
   - Class not full
   - No duplicate booking
   - Membership active
6. Show booking confirmation
```

#### 5. **Member View/Cancel Bookings**

```
Flow:
1. Member identifies themselves
2. GET /classes/bookings?member_id={id}
3. Display list of bookings
4. To cancel: PATCH /classes/bookings/{id}/cancel/
```

#### 6. **Member Payment Flow**

```
Flow:
1. Member identifies themselves
2. Select payment option:
   - "Pay for Today's Session" (links to attendance)
   - "Renew Membership" (no attendance link)
3. Payment form:
   - Amount (auto-filled from membership plan)
   - Payment method (cash/card/mobile money)
   - Transaction ID (optional)
4. Submit → POST /payments/
   Body: {
     member_id: uuid,
     payment_method: "mobile_money",
     attendance_id: uuid (optional),
     transaction_id: "TXN123"
   }
5. Show receipt/confirmation
```

### STAFF FLOWS (Requires Authentication)

#### 1. **Staff Login & Dashboard**

```
Flow:
1. Login page → Enter email/password → POST /auth/gimme-jwt/
2. Store JWT token in localStorage
3. Redirect to Dashboard
4. Dashboard shows:
   - Total active members
   - Today's check-ins
   - Today's revenue
   - Upcoming classes
   - Equipment needing maintenance
```

#### 2. **Staff Manage Members**

```
Flow:
1. View all members → GET /members/ (paginated)
2. Search by name → GET /members/?search={name}
3. Filter by status → GET /members/?is_active=true
4. Create/Edit/Delete members
5. View member details and history
```

---

## Critical Implementation Details

### 1. **Axios Configuration (No Auth for Member Routes)**

```typescript
// api/axios.config.ts
import axios from "axios";

const api = axios.create({
  baseURL: "http://127.0.0.1:8000",
  headers: {
    "Content-Type": "application/json",
  },
});

// Staff-only routes that need authentication
const staffRoutes = ["/accounts", "/inventory"];

api.interceptors.request.use(
  (config) => {
    const isStaffRoute = staffRoutes.some((route) =>
      config.url?.startsWith(route)
    );

    if (isStaffRoute) {
      const token = localStorage.getItem("access_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const isStaffRoute = staffRoutes.some((route) =>
        error.config.url?.startsWith(route)
      );
      if (isStaffRoute) {
        localStorage.removeItem("access_token");
        window.location.href = "/staff/login";
      }
    }
    return Promise.reject(error);
  }
);

export default api;
```

### 2. **Member Context (No Authentication)**

```typescript
// context/MemberContext.tsx
import { createContext, useContext, useState } from "react";
import { Member } from "../types/member.types";

interface MemberContextType {
  currentMember: Member | null;
  setCurrentMember: (member: Member | null) => void;
  clearMember: () => void;
}

const MemberContext = createContext<MemberContextType | undefined>(undefined);

export const MemberProvider = ({ children }) => {
  const [currentMember, setCurrentMember] = useState<Member | null>(() => {
    const stored = sessionStorage.getItem("current_member");
    return stored ? JSON.parse(stored) : null;
  });

  const handleSetMember = (member: Member | null) => {
    setCurrentMember(member);
    if (member) {
      sessionStorage.setItem("current_member", JSON.stringify(member));
    } else {
      sessionStorage.removeItem("current_member");
    }
  };

  const clearMember = () => {
    setCurrentMember(null);
    sessionStorage.removeItem("current_member");
  };

  return (
    <MemberContext.Provider
      value={{
        currentMember,
        setCurrentMember: handleSetMember,
        clearMember,
      }}
    >
      {children}
    </MemberContext.Provider>
  );
};

export const useMember = () => {
  const context = useContext(MemberContext);
  if (!context) {
    throw new Error("useMember must be used within MemberProvider");
  }
  return context;
};
```

### 3. **Staff Authentication Hook**

```typescript
// hooks/useAuth.ts
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../api/axios.config";
import { User } from "../types/user.types";

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem("access_token");
    if (token) {
      fetchCurrentUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const { data } = await api.get("/accounts/me/");
      setUser(data);
    } catch (error) {
      localStorage.removeItem("access_token");
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const { data } = await api.post("/auth/gimme-jwt/", { email, password });
    localStorage.setItem("access_token", data.access_token);
    await fetchCurrentUser();
    navigate("/staff/dashboard");
  };

  const logout = () => {
    localStorage.removeItem("access_token");
    setUser(null);
    navigate("/staff/login");
  };

  return { user, loading, login, logout };
};
```

### 4. **Member Identification Hook**

```typescript
// hooks/useMemberIdentify.ts
import { useState } from "react";
import api from "../api/axios.config";
import { Member } from "../types/member.types";
import { useMember } from "../context/MemberContext";

export const useMemberIdentify = () => {
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setCurrentMember } = useMember();

  const findMemberByPhone = async (phone: string): Promise<Member | null> => {
    setSearching(true);
    setError(null);

    try {
      const { data } = await api.get(`/members/?phone=${phone}`);

      if (data.items && data.items.length > 0) {
        const member = data.items[0];
        setCurrentMember(member);
        return member;
      } else {
        setError("No member found with this phone number");
        return null;
      }
    } catch (err) {
      setError("Error searching for member");
      return null;
    } finally {
      setSearching(false);
    }
  };

  const findMemberByEmail = async (email: string): Promise<Member | null> => {
    setSearching(true);
    setError(null);

    try {
      const { data } = await api.get(`/members/?email=${email}`);

      if (data.items && data.items.length > 0) {
        const member = data.items[0];
        setCurrentMember(member);
        return member;
      } else {
        setError("No member found with this email");
        return null;
      }
    } catch (err) {
      setError("Error searching for member");
      return null;
    } finally {
      setSearching(false);
    }
  };

  return { findMemberByPhone, findMemberByEmail, searching, error };
};
```

### 5. **Pagination Handling**

````typescript
// Handle FastAPI pagination response
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Example in component
const MemberList = () => {
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);

  const { data, isLoading } = useQuery<PaginatedResponse<Member>>(
    ['members', page, pageSize],
    () => api.get(`/members/?page=${page}&size=${pageSize}`).then(res => res.data)
  );

  return (
    <div>
      {data?.items.map(member => (
        <MemberCard key={member.id} member={member} />
      ))}

      <Pagination
        current={data?.page}
        total={data?.pages}
        onChange={setPage}
      />
    </div>
  );
};
``` Configuration**
```typescript
// api/axios.config.ts
import axios from "axios";

const api = axios.create({
  baseURL: "http://127.0.0.1:8000",
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor - add JWT token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor - handle 401 errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
````

### 2. **Authentication Hook**

```typescript
// hooks/useAuth.ts
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../api/axios.config";
import { User } from "../types/user.types";

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem("access_token");
    if (token) {
      fetchCurrentUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const { data } = await api.get("/accounts/me/");
      setUser(data);
    } catch (error) {
      localStorage.removeItem("access_token");
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const { data } = await api.post("/auth/gimme-jwt/", { email, password });
    localStorage.setItem("access_token", data.access_token);
    await fetchCurrentUser();
    navigate("/dashboard");
  };

  const logout = () => {
    localStorage.removeItem("access_token");
    setUser(null);
    navigate("/login");
  };

  return { user, loading, login, logout };
};
```

### 3. **Pagination Handling**

```typescript
// All list endpoints return paginated data
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Example usage in component
const [page, setPage] = useState(1);
const { data, isLoading } = useQuery(["members", page], () =>
  api.get(`/members/?page=${page}&size=20`)
);
```

### 4. **Date Formatting**

```typescript
// utils/formatters.ts
import { format, parseISO } from "date-fns";

export const formatDate = (dateString: string) => {
  return format(parseISO(dateString), "MMM dd, yyyy");
};

export const formatDateTime = (dateString: string) => {
  return format(parseISO(dateString), "MMM dd, yyyy hh:mm a");
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};
```

### 5. **Role-Based Access Control**

```typescript
// components/common/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
}

export const ProtectedRoute = ({
  children,
  allowedRoles,
}: ProtectedRouteProps) => {
  const { user, loading } = useAuth();

  if (loading) return <LoadingSpinner />;
  if (!user) return <Navigate to="/login" />;
  if (!allowedRoles.includes(user.role)) {
    return <div>Access Denied</div>;
  }

  return <>{children}</>;
};

// Usage in routes
<Route
  path="/inventory"
  element={
    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.MANAGER]}>
      <InventoryPage />
    </ProtectedRoute>
  }
/>;
```

---

## UI/UX Requirements

### Member UI (Mobile-First Design - Primary Users on Phone/Tablet)

#### 1. **Mobile Navigation**

- Bottom navigation bar (fixed position)
- Large touch targets (min 44px)
- Simple 4-5 main options:
  - Home
  - Classes
  - My Bookings
  - Check-In
  - Profile

#### 2. **Member Search/Identification Component** (Critical)

```typescript
<MemberIdentify
  onMemberFound={(member) => handleMemberFound(member)}
  searchPlaceholder="Enter your phone or email"
/>

Features:
- Large input field (mobile-friendly)
- Auto-focus on mount
- Search by phone or email
- Shows: name, membership status, expiry date
- Clear "Confirm" button
- Store member in session/context for subsequent actions
```

#### 3. **Class Booking Card (Mobile)**

- Large card design
- Clear class name and time
- Visual capacity indicator (15/20 spots)
- One-tap booking button
- Swipe gestures for navigation

#### 4. **Payment Screen (Mobile)**

- Step-by-step wizard
- Large numeric keypad for amounts
- Payment method selector with icons
- Clear receipt display
- Share/download receipt option

#### 5. **Responsive Breakpoints**

```css
/* Mobile First */
Base styles: 320px - 767px (phones)
Tablet: 768px - 1023px
Desktop: 1024px+ (rarely used by members)
```

### Staff UI (Desktop-First Design)

#### 1. **Dashboard KPI Cards**

- Active Members Today
- Total Check-ins (Today/Week/Month)
- Revenue (Today/Week/Month)
- Upcoming Classes
- Equipment Status Summary

#### 2. **Data Tables**

- Sortable columns
- Inline filters
- Pagination controls
- Bulk actions
- Export functionality

#### 3. **Sidebar Navigation**

- Collapsible menu
- Role-based menu items
- Current page highlight
- User profile dropdown

---

## Form Validation Rules

### Member Form

- Name: Required, min 2 characters
- Email: Valid email format (optional)
- Phone: Required, valid phone format
- Membership plan: Required
- Start date: Required if plan selected

### Payment Form

- Member: Required
- Payment method: Required
- Amount: Auto-filled from plan, read-only
- Transaction ID: Optional

### Class Form

- Name: Required
- Capacity: Required, min 1
- Duration: Required, min 15 minutes
- Day of week: Required dropdown
- Start time: Required time picker

### Booking Form

- Class: Required
- Member: Required
- Date: Required, cannot be past date
- Validates: Class capacity not exceeded

---

## Error Handling Strategy

```typescript
// Centralized error handler
const handleApiError = (error: any) => {
  if (error.response) {
    // Server responded with error
    const { status, data } = error.response;

    if (status === 404) {
      toast.error(data.detail || "Resource not found");
    } else if (status === 400) {
      toast.error(data.detail || "Invalid request");
    } else if (status === 500) {
      toast.error("Server error. Please try again.");
    }
  } else if (error.request) {
    // Request made but no response
    toast.error("Network error. Check your connection.");
  } else {
    toast.error("An unexpected error occurred.");
  }
};
```

---

## Testing Checklist

1. **Authentication**

   - Login with valid/invalid credentials
   - Token expiration handling
   - Protected route access

2. **Member Operations**

   - Create member with/without plan
   - Search functionality
   - Update member info
   - Delete member (check cascade)

3. **Check-in Flow**

   - Prevent duplicate check-ins
   - Check membership expiry
   - Check-out duration calculation

4. **Payment Processing**

   - Payment with attendance
   - Payment without attendance
   - Validate member exists
   - Transaction ID handling

5. **Class Booking**

   - Capacity validation
   - Duplicate booking prevention
   - Cancel booking
   - No-show handling

6. **Edge Cases**
   - Expired memberships
   - Full class bookings
   - Network errors
   - Concurrent operations

---

## Routes Configuration

```typescript
// App.tsx
const AppRoutes = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* MEMBER ROUTES - Public, No Auth, Mobile-First */}
        <Route path="/" element={<Navigate to="/member" replace />} />
        <Route path="/member" element={<MemberHomePage />} />
        <Route path="/member/find" element={<FindMemberPage />} />
        <Route path="/member/profile" element={<MemberProfilePage />} />
        <Route path="/member/classes" element={<ClassSchedulePage />} />
        <Route path="/member/book" element={<BookClassPage />} />
        <Route path="/member/bookings" element={<MyBookingsPage />} />
        <Route path="/member/checkin" element={<CheckInPage />} />
        <Route path="/member/payment" element={<PaymentPage />} />

        {/* STAFF ROUTES - Protected, Auth Required, Desktop-First */}
        <Route path="/staff/login" element={<LoginPage />} />

        <Route
          path="/staff/dashboard"
          element={
            <ProtectedRoute allowedRoles={Object.values(UserRole)}>
              <DashboardPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/staff/members"
          element={
            <ProtectedRoute allowedRoles={Object.values(UserRole)}>
              <MembersPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/staff/attendance"
          element={
            <ProtectedRoute
              allowedRoles={[
                UserRole.RECEPTIONIST,
                UserRole.ADMIN,
                UserRole.MANAGER,
              ]}
            >
              <AttendancePage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/staff/payments"
          element={
            <ProtectedRoute
              allowedRoles={[
                UserRole.RECEPTIONIST,
                UserRole.ACCOUNTANT,
                UserRole.ADMIN,
                UserRole.MANAGER,
              ]}
            >
              <PaymentsPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/staff/classes"
          element={
            <ProtectedRoute
              allowedRoles={[
                UserRole.TRAINER,
                UserRole.ADMIN,
                UserRole.MANAGER,
              ]}
            >
              <ClassesPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/staff/inventory"
          element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.MANAGER]}>
              <InventoryPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/staff/settings"
          element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
              <SettingsPage />
            </ProtectedRoute>
          }
        />
      </Routes>
    </BrowserRouter>
  );
};
```

---
