// Re-export all API modules for easy access
export { authApi } from "../api/auth.api";
export { membersApi, membershipPlansApi } from "../api/members.api";
export { attendanceApi } from "../api/attendance.api";
export { paymentsApi } from "../api/payments.api";
export { classesApi, bookingsApi } from "../api/classes.api";
export { inventoryApi } from "../api/inventory.api";
export { usersApi } from "../api/users.api";

// Legacy compatibility - keeping some functions for backward compatibility
import { membersApi } from "../api/members.api";
import { attendanceApi } from "../api/attendance.api";

// @deprecated - use membersApi.getMembers() instead
export const getAllMembers = async () => {
  const response = await membersApi.getMembers();
  return response.items;
};

// @deprecated - use attendanceApi.getAttendance() instead
export const getAttendance = async () => {
  const response = await attendanceApi.getAttendance();
  return response.items;
};
