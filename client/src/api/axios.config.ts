import axios from "axios";

// baseURL: import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/",

const api = axios.create({
  baseURL: "http://127.0.0.1:8000/",
  headers: {
    "Content-Type": "application/json",
  },
});

// Staff-only routes that need authentication
const staffRoutes = ["/accounts", "/inventory"];

// Routes that require staff authentication for certain methods
const staffProtectedMethods = {
  "/members": ["DELETE"],
  "/membership-plans": ["POST", "PUT", "DELETE"],
  "/attendance": ["DELETE"],
  "/payments": ["DELETE"],
  "/classes": ["POST", "PUT", "DELETE"],
  "/classes/bookings": ["DELETE"],
};

api.interceptors.request.use(
  (config) => {
    const isStaffRoute = staffRoutes.some((route) =>
      config.url?.startsWith(route)
    );

    // Check if this is a staff-protected method
    const isStaffProtectedMethod = Object.entries(staffProtectedMethods).some(
      ([route, methods]) =>
        config.url?.startsWith(route) && 
        methods.includes(config.method?.toUpperCase() || "GET")
    );

    if (isStaffRoute || isStaffProtectedMethod) {
      const token = localStorage.getItem("access_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const isStaffRoute = staffRoutes.some((route) =>
        error.config.url?.startsWith(route)
      );
      
      const isStaffProtectedMethod = Object.entries(staffProtectedMethods).some(
        ([route, methods]) =>
          error.config.url?.startsWith(route) && 
          methods.includes(error.config.method?.toUpperCase() || "GET")
      );

      if (isStaffRoute || isStaffProtectedMethod) {
        localStorage.removeItem("access_token");
        window.location.href = "/staff/login";
      }
    }
    return Promise.reject(error);
  }
);

export default api;
