import api from "./axios.config";
import { LoginRequest, LoginResponse, User } from "../types/api";

export const authApi = {
  // Staff login
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const { data } = await api.post("/auth/gimme-jwt/", credentials);
    return data;
  },

  // Register new staff user
  register: async (userData: {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    phone?: string;
    role: string;
  }): Promise<User> => {
    const { data } = await api.post("/auth/register/", userData);
    return data;
  },

  // Get current logged-in user
  getCurrentUser: async (): Promise<User> => {
    const { data } = await api.get("/accounts/me/");
    return data;
  },
};
